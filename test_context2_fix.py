#!/usr/bin/env python3
"""
Test script to verify Context 2.0 workflow KeyError fix.
"""

import asyncio
import json
import sys
import os

# Add the gitlab-ai-gateway directory to the path
sys.path.insert(0, '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway')

async def test_context2_workflow():
    """Test the Context 2.0 workflow to see if KeyError is fixed."""
    try:
        # Import the workflow
        from duo_workflow_service.agents.context_2_0.context_2_workflow import Context2Workflow
        from duo_workflow_service.agents.context_2_0.enhanced_state import Context2StateManager
        from duo_workflow_service.tools import Toolset
        from duo_workflow_service.components.tools_registry import ToolsRegistry
        from duo_workflow_service.components.prompt_registry import PromptRegistry
        
        print("✅ Successfully imported Context2Workflow")
        
        # Create a minimal toolset for testing
        tools_registry = ToolsRegistry()
        toolset = Toolset(tools_registry)
        
        # Create a minimal prompt registry
        prompt_registry = PromptRegistry()
        
        # Initialize the workflow
        workflow = Context2Workflow(
            toolset=toolset,
            prompt_registry=prompt_registry,
            workflow_id="test-workflow-123",
            workflow_type="software_development_2_0"
        )
        
        print("✅ Successfully initialized Context2Workflow")
        
        # Test state initialization
        test_goal = "Analyze the GitLab codebase structure"
        initial_state = Context2StateManager.initialize_context2_state(test_goal)
        
        print("✅ Successfully initialized Context2State")
        print(f"Initial state keys: {list(initial_state.keys())}")
        
        # Test the routing functions directly
        print("\n🔍 Testing routing functions...")
        
        # Test _route_from_orchestrator
        test_state = initial_state.copy()
        test_state["orchestration_phase"] = "completed"
        result = workflow._route_from_orchestrator(test_state)
        print(f"_route_from_orchestrator with 'completed' phase: {result}")
        
        # Test _should_continue_investigation
        test_state["current_agent"] = "Repository Explorer"
        result = workflow._should_continue_investigation(test_state)
        print(f"_should_continue_investigation with Repository Explorer: {result}")
        
        # Test _route_agent_to_tools
        result = workflow._route_agent_to_tools(test_state)
        print(f"_route_agent_to_tools with Repository Explorer: {result}")
        
        print("\n✅ All routing functions working correctly!")
        print("🎉 KeyError fix appears to be successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Context2Workflow: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_context2_workflow())
    sys.exit(0 if success else 1)
