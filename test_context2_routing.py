#!/usr/bin/env python3
"""
Test script to verify Context 2.0 workflow routing functions.
"""

import sys
import os

# Add the gitlab-ai-gateway directory to the path
sys.path.insert(0, '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway')

def test_routing_functions():
    """Test the routing functions directly to verify KeyError fix."""
    try:
        # Import the workflow module directly
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "context_2_workflow", 
            "/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway/********************/agents/context_2_0/context_2_workflow.py"
        )
        context_2_workflow = importlib.util.module_from_spec(spec)
        
        print("✅ Successfully loaded context_2_workflow module")
        
        # Check if the functions exist and have correct signatures
        if hasattr(context_2_workflow, 'Context2Workflow'):
            workflow_class = getattr(context_2_workflow, 'Context2Workflow')
            print("✅ Context2Workflow class found")
            
            # Check if the routing methods exist
            methods_to_check = [
                '_should_continue_investigation',
                '_route_agent_to_tools', 
                '_route_from_orchestrator',
                '_route_from_quality_gate'
            ]
            
            for method_name in methods_to_check:
                if hasattr(workflow_class, method_name):
                    method = getattr(workflow_class, method_name)
                    print(f"✅ Method {method_name} exists")
                    
                    # Check the method signature
                    import inspect
                    sig = inspect.signature(method)
                    params = list(sig.parameters.keys())
                    return_annotation = sig.return_annotation
                    
                    print(f"   Parameters: {params}")
                    print(f"   Return type: {return_annotation}")
                    
                    # Verify _should_continue_investigation returns str, not bool
                    if method_name == '_should_continue_investigation':
                        if return_annotation == str:
                            print("   ✅ Returns str (correct for routing)")
                        else:
                            print(f"   ❌ Returns {return_annotation} (should be str)")
                else:
                    print(f"❌ Method {method_name} not found")
            
            print("\n🔍 Checking for duplicate method definitions...")
            
            # Read the file and check for duplicate method names
            with open('/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway/********************/agents/context_2_0/context_2_workflow.py', 'r') as f:
                content = f.read()
                
            import re
            method_pattern = r'def (_should_continue_investigation)\('
            matches = re.findall(method_pattern, content)
            
            if len(matches) > 1:
                print(f"❌ Found {len(matches)} definitions of _should_continue_investigation")
            else:
                print("✅ Only one definition of _should_continue_investigation found")
                
            # Check for the renamed method
            if '_check_quality_thresholds' in content:
                print("✅ Found _check_quality_thresholds method (renamed duplicate)")
            else:
                print("❌ _check_quality_thresholds method not found")
                
            print("\n🎉 Routing function analysis complete!")
            return True
        else:
            print("❌ Context2Workflow class not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing routing functions: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_routing_functions()
    sys.exit(0 if success else 1)
