#!/usr/bin/env python3
"""
Test script to verify Context 2.0 infinite loop fixes.

This script tests the key components that were fixed:
1. Base specialist agent investigate() method
2. Context 2.0 workflow routing logic
3. Integration with Software Development 2.0
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the gitlab-ai-gateway directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "gitlab-ai-gateway"))

async def test_base_specialist_agent():
    """Test the base specialist agent investigate method."""
    print("🧪 Testing Base Specialist Agent...")
    
    try:
        from duo_workflow_service.agents.context_2_0.base_specialist_agent import BaseSpecialistAgent
        from duo_workflow_service.tools.handover import HandoverTool
        
        # Test that the investigate method signature is correct
        import inspect
        sig = inspect.signature(BaseSpecialistAgent.investigate)
        params = list(sig.parameters.keys())
        
        expected_params = ['self', 'query', 'context', 'use_tools_executor', 'tool_results']
        if all(param in params for param in expected_params):
            print("✅ investigate() method has correct signature")
        else:
            print(f"❌ investigate() method missing parameters. Found: {params}")
            return False
            
        # Test that _create_handover_message method exists
        if hasattr(BaseSpecialistAgent, '_create_handover_message'):
            print("✅ _create_handover_message() method exists")
        else:
            print("❌ _create_handover_message() method missing")
            return False
            
        print("✅ Base Specialist Agent tests passed")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_context_2_workflow():
    """Test the Context 2.0 workflow routing logic."""
    print("\n🧪 Testing Context 2.0 Workflow...")
    
    try:
        from duo_workflow_service.agents.context_2_0.context_2_workflow import Context2Workflow
        
        # Test that the new routing method exists
        if hasattr(Context2Workflow, '_route_agent_decision'):
            print("✅ _route_agent_decision() method exists")
        else:
            print("❌ _route_agent_decision() method missing")
            return False
            
        # Test that the old problematic method is removed
        if not hasattr(Context2Workflow, '_should_continue_investigation'):
            print("✅ _should_continue_investigation() method removed")
        else:
            print("❌ _should_continue_investigation() method still exists")
            return False
            
        # Test that invoke method exists
        if hasattr(Context2Workflow, 'invoke'):
            print("✅ invoke() method exists")
        else:
            print("❌ invoke() method missing")
            return False
            
        print("✅ Context 2.0 Workflow tests passed")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_software_development_2_0_integration():
    """Test Software Development 2.0 integration."""
    print("\n🧪 Testing Software Development 2.0 Integration...")
    
    try:
        from duo_workflow_service.workflows.software_development_2_0.workflow import SoftwareDevelopment2Workflow
        
        # Test that Context2Workflow is imported
        import duo_workflow_service.workflows.software_development_2_0.workflow as sd2_module
        if hasattr(sd2_module, 'Context2Workflow'):
            print("✅ Context2Workflow imported in Software Development 2.0")
        else:
            print("❌ Context2Workflow not imported")
            return False
            
        # Test that the workflow has the setup method
        if hasattr(SoftwareDevelopment2Workflow, '_setup_context_builder'):
            print("✅ _setup_context_builder() method exists")
        else:
            print("❌ _setup_context_builder() method missing")
            return False
            
        print("✅ Software Development 2.0 Integration tests passed")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_specialist_agents():
    """Test that specialist agents don't override investigate method."""
    print("\n🧪 Testing Specialist Agents...")
    
    agents = [
        'repository_explorer',
        'code_navigator', 
        'git_history',
        'gitlab_ecosystem',
        'context_synthesizer'
    ]
    
    try:
        for agent_name in agents:
            module_path = f"duo_workflow_service.agents.context_2_0.{agent_name}"
            module = __import__(module_path, fromlist=[''])
            
            # Get the agent class (should be CamelCase version of agent_name)
            class_name = ''.join(word.capitalize() for word in agent_name.split('_'))
            if hasattr(module, class_name):
                agent_class = getattr(module, class_name)
                
                # Check if it overrides investigate method
                if 'investigate' in agent_class.__dict__:
                    print(f"❌ {class_name} overrides investigate() method")
                    return False
                else:
                    print(f"✅ {class_name} uses base investigate() method")
            else:
                print(f"❌ {class_name} class not found in {agent_name}.py")
                return False
                
        print("✅ All specialist agents use base investigate() method")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Starting Context 2.0 Fix Verification Tests\n")
    
    tests = [
        ("Base Specialist Agent", test_base_specialist_agent),
        ("Context 2.0 Workflow", test_context_2_workflow),
        ("Software Development 2.0 Integration", test_software_development_2_0_integration),
        ("Specialist Agents", test_specialist_agents),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<40} {status}")
        if result:
            passed += 1
    
    print("="*60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Context 2.0 fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
