#!/usr/bin/env python3
"""
Test script to make a request to GitLab DAP and verify Context 2.0 workflow.
"""

import requests
import json
import time

def test_dap_request():
    """Test making a request to GitLab DAP to trigger Context 2.0 workflow."""
    
    # AI Gateway endpoint
    base_url = "http://127.0.0.1:5052"
    
    # Test payload for duo workflow
    payload = {
        "model": "claude-sonnet-4",
        "messages": [
            {
                "role": "user", 
                "content": "Analyze the GitLab codebase structure and tell me about the main components"
            }
        ],
        "stream": False,
        "workflow_definition": "software_development_2_0"  # This should trigger Context 2.0
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"  # Placeholder
    }
    
    try:
        print("🚀 Making request to GitLab DAP...")
        print(f"URL: {base_url}/v1/duo_workflow")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            f"{base_url}/v1/duo_workflow",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            try:
                response_data = response.json()
                print(f"Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"Response text: {response.text}")
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_dap_request()
    print(f"\n{'🎉 Test passed!' if success else '❌ Test failed!'}")
