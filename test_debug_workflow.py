#!/usr/bin/env python3
"""
Test script to trigger the Context 2.0 workflow with debug logging
to understand why the infinite loop is still happening.
"""

import sys
import os
import asyncio

# Add the gitlab-ai-gateway directory to the path
sys.path.insert(0, '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway')

async def test_simple_workflow_execution():
    """Test a simple workflow execution to see debug output."""
    try:
        from duo_workflow_service.agents.context_2_0.context_2_workflow import Context2Workflow
        from duo_workflow_service.agents.context_2_0.enhanced_state import Context2StateManager
        from duo_workflow_service.entities import WorkflowTypeEnum
        
        print("🧪 Testing Context 2.0 workflow with debug logging...")
        
        # Create a minimal workflow instance
        workflow = Context2Workflow(
            workflow_id="test-debug-workflow",
            workflow_type=WorkflowTypeEnum.SOFTWARE_DEVELOPMENT_2_0,
            toolset=None,  # We'll mock this
            tools_registry=None,  # We'll mock this
            prompt_registry=None,  # We'll mock this
            user=None,  # We'll mock this
            http_client=None  # We'll mock this
        )
        
        # Create initial state
        initial_state = Context2StateManager.initialize_context2_state("Test the repository structure")
        
        print("✅ Workflow instance created successfully")
        print("✅ Initial state created successfully")
        
        # We can't actually run the workflow without proper dependencies,
        # but we can test the state management and debug logging
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing workflow: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_conversation_history_reducer_with_debug():
    """Test the conversation history reducer with debug logging."""
    try:
        from duo_workflow_service.entities.state import _conversation_history_reducer
        from langchain_core.messages import AIMessage, ToolMessage
        
        print("\n🔀 Testing conversation history reducer with debug logging...")
        
        # Create initial conversation history
        current = {
            "repository_explorer": [
                AIMessage(
                    content="I need to investigate the repository.",
                    tool_calls=[{"name": "list_dir", "args": {"path": "."}, "id": "call_1"}]
                )
            ]
        }
        
        # Create new tool result
        new = {
            "repository_explorer": [
                ToolMessage(
                    content="Directory contents: file1.py, file2.py",
                    tool_call_id="call_1",
                    name="list_dir"
                )
            ]
        }
        
        print("Calling conversation history reducer...")
        result = _conversation_history_reducer(current, new)
        
        print(f"Result: {len(result['repository_explorer'])} messages in conversation history")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing reducer: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debug tests."""
    print("🐛 Debug Testing Context 2.0 Workflow")
    print("=" * 45)
    
    tests = [
        ("Conversation History Reducer Debug", test_conversation_history_reducer_with_debug),
        ("Simple Workflow Execution", lambda: asyncio.run(test_simple_workflow_execution())),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 35)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 45)
    print(f"📊 Debug Test Results: {passed}/{total} tests passed")
    
    print("\n💡 Debug logging has been added to:")
    print("   • context_2_workflow.py: _create_specialist_agent_node method")
    print("   • base_specialist_agent.py: investigate method")
    print("   • state.py: _conversation_history_reducer function")
    print("\n🔧 Next step: Run an actual Context 2.0 workflow to see debug output")

if __name__ == "__main__":
    main()
