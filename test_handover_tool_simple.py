#!/usr/bin/env python3
"""
Simple test to verify HandoverTool is properly configured in Context 2.0.
This test avoids circular imports by testing the configuration directly.
"""

import sys
import os

# Add the gitlab-ai-gateway directory to the path
sys.path.insert(0, '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway')

def test_handover_tool_in_distribution():
    """Test that HandoverTool is included in the tool distribution system."""
    try:
        # Read the tool distribution file directly
        distribution_file = '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway/********************/agents/context_2_0/tool_distribution_system.py'
        
        with open(distribution_file, 'r') as f:
            content = f.read()
        
        print("🔧 Checking HandoverTool in tool distribution system...")
        
        # Check if handover_tool is defined in the tool specs
        if '"handover_tool": ToolSpec(' in content:
            print("✅ HandoverTool is defined in tool specifications")
        else:
            print("❌ HandoverTool NOT found in tool specifications")
            return False
        
        # Check if handover_tool is marked as shared_across_agents=True
        if 'shared_across_agents=True' in content and '"handover_tool"' in content:
            print("✅ HandoverTool is marked as shared across agents")
        else:
            print("❌ HandoverTool is NOT marked as shared across agents")
            return False
        
        # Check each specialist agent has handover_tool in shared_tools
        specialist_agents = [
            "Repository Explorer",
            "Code Navigator", 
            "GitLab Ecosystem",
            "Git History"
        ]
        
        for agent in specialist_agents:
            # Look for the agent's shared_tools section
            agent_section_start = content.find(f'"{agent}": {{')
            if agent_section_start == -1:
                print(f"❌ {agent} section not found")
                return False
            
            # Find the shared_tools section for this agent
            shared_tools_start = content.find('"shared_tools": [', agent_section_start)
            next_agent_start = content.find('": {', agent_section_start + 1)
            
            if shared_tools_start == -1 or (next_agent_start != -1 and shared_tools_start > next_agent_start):
                print(f"❌ {agent} shared_tools section not found")
                return False
            
            # Find the end of the shared_tools array
            shared_tools_end = content.find(']', shared_tools_start)
            shared_tools_section = content[shared_tools_start:shared_tools_end + 1]
            
            if '"handover_tool"' in shared_tools_section:
                print(f"✅ {agent}: HandoverTool found in shared_tools")
            else:
                print(f"❌ {agent}: HandoverTool MISSING from shared_tools")
                print(f"   Shared tools section: {shared_tools_section}")
                return False
        
        # Check Context Synthesizer has handover_tool in primary_tools
        context_synth_start = content.find('"Context Synthesizer": {')
        if context_synth_start == -1:
            print("❌ Context Synthesizer section not found")
            return False
        
        primary_tools_start = content.find('"primary_tools": [', context_synth_start)
        primary_tools_end = content.find(']', primary_tools_start)
        primary_tools_section = content[primary_tools_start:primary_tools_end + 1]
        
        if '"handover_tool"' in primary_tools_section:
            print("✅ Context Synthesizer: HandoverTool found in primary_tools")
        else:
            print("❌ Context Synthesizer: HandoverTool MISSING from primary_tools")
            return False
        
        print("✅ All agents have HandoverTool properly configured!")
        return True
        
    except Exception as e:
        print(f"❌ Error checking tool distribution: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_routing_logic_fix():
    """Test that the routing logic has been updated to handle HandoverTool."""
    try:
        # Read the context_2_workflow file directly
        workflow_file = '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway/********************/agents/context_2_0/context_2_workflow.py'
        
        with open(workflow_file, 'r') as f:
            content = f.read()
        
        print("\n🔀 Checking routing logic for HandoverTool handling...")
        
        # Check if HandoverTool import is present
        if 'from ********************.tools.handover import HandoverTool' in content:
            print("✅ HandoverTool import found in routing logic")
        else:
            print("❌ HandoverTool import MISSING from routing logic")
            return False
        
        # Check if HandoverTool.tool_title check is present
        if 'HandoverTool.tool_title' in content:
            print("✅ HandoverTool.tool_title check found in routing logic")
        else:
            print("❌ HandoverTool.tool_title check MISSING from routing logic")
            return False
        
        # Check if the routing logic checks for HandoverTool before routing to tools
        route_method_start = content.find('def _route_agent_to_tools(')
        if route_method_start == -1:
            print("❌ _route_agent_to_tools method not found")
            return False
        
        # Find the end of the method
        next_method_start = content.find('\n    def ', route_method_start + 1)
        route_method_content = content[route_method_start:next_method_start] if next_method_start != -1 else content[route_method_start:]
        
        if 'if last_message.tool_calls[0]["name"] == HandoverTool.tool_title:' in route_method_content:
            print("✅ Routing logic correctly checks for HandoverTool calls")
        else:
            print("❌ Routing logic does NOT check for HandoverTool calls")
            return False
        
        print("✅ Routing logic has been properly updated!")
        return True
        
    except Exception as e:
        print(f"❌ Error checking routing logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run tests to verify the HandoverTool fix."""
    print("🧪 Testing Context 2.0 HandoverTool Fix (Simple)")
    print("=" * 55)
    
    tests = [
        ("HandoverTool in Distribution System", test_handover_tool_in_distribution),
        ("Routing Logic Fix", test_routing_logic_fix),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 35)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 55)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The HandoverTool fix should resolve the infinite loop issue.")
        print("\n💡 Summary of fixes applied:")
        print("   1. ✅ Added 'handover_tool' to all specialist agents' shared_tools")
        print("   2. ✅ Updated routing logic to check for HandoverTool calls")
        print("   3. ✅ Agents can now properly signal completion and avoid infinite loops")
        print("\n🚀 The Context 2.0 workflow should now work correctly without infinite loops!")
        return True
    else:
        print("⚠️  Some tests failed. The fix may need additional work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
