#!/usr/bin/env python3
"""
Test script to verify that the Context 2.0 HandoverTool fix resolves the infinite loop issue.

This script tests:
1. That specialist agents now have HandoverTool in their toolsets
2. That the routing logic correctly handles HandoverTool calls
3. That agents can properly signal completion
"""

import sys
import os
import asyncio
from unittest.mock import Mock, MagicMock

# Add the gitlab-ai-gateway directory to the path
sys.path.insert(0, '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway')

def test_handover_tool_in_toolsets():
    """Test that all specialist agents now have HandoverTool in their toolsets."""
    try:
        from duo_workflow_service.agents.context_2_0.tool_distribution_system import ToolDistributionSystem
        
        print("🔧 Testing HandoverTool in specialist agent toolsets...")
        
        distribution_system = ToolDistributionSystem()
        
        # Check each specialist agent
        specialist_agents = ["Repository Explorer", "Code Navigator", "GitLab Ecosystem", "Git History", "Context Synthesizer"]
        
        for agent_name in specialist_agents:
            agent_tools = distribution_system.get_tools_for_agent(agent_name)
            all_tools = agent_tools["tool_names"]
            
            if "handover_tool" in all_tools:
                print(f"✅ {agent_name}: HandoverTool found in toolset")
            else:
                print(f"❌ {agent_name}: HandoverTool MISSING from toolset")
                print(f"   Available tools: {all_tools}")
                return False
        
        print("✅ All specialist agents have HandoverTool in their toolsets!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing toolsets: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_routing_logic():
    """Test that the routing logic correctly handles HandoverTool calls."""
    try:
        from duo_workflow_service.agents.context_2_0.context_2_workflow import Context2Workflow
        from duo_workflow_service.tools.handover import HandoverTool
        from langchain_core.messages import AIMessage
        
        print("\n🔀 Testing routing logic for HandoverTool...")
        
        # Create a mock workflow instance
        workflow = Mock(spec=Context2Workflow)
        
        # Import the actual method
        from duo_workflow_service.agents.context_2_0.context_2_workflow import Context2Workflow
        
        # Create a real instance for testing the method
        # We'll mock the dependencies
        mock_toolset = Mock()
        mock_tools_registry = Mock()
        
        # Create test state with HandoverTool call
        test_state = {
            "current_agent": "Repository Explorer",
            "conversation_history": {
                "repository_explorer": [
                    AIMessage(
                        content="I have completed my investigation.",
                        tool_calls=[{"name": HandoverTool.tool_title, "args": {"summary": "Investigation complete"}}]
                    )
                ]
            }
        }
        
        # Test the routing method directly
        # We need to create a minimal workflow instance
        workflow_instance = Context2Workflow.__new__(Context2Workflow)
        
        # Test the routing
        result = workflow_instance._route_agent_to_tools(test_state)
        
        if result == "orchestrator":
            print("✅ HandoverTool correctly routes to orchestrator")
        else:
            print(f"❌ HandoverTool incorrectly routes to: {result}")
            return False
        
        # Test with regular tool call
        test_state_regular = {
            "current_agent": "Repository Explorer", 
            "conversation_history": {
                "repository_explorer": [
                    AIMessage(
                        content="I need to read a file.",
                        tool_calls=[{"name": "read_file", "args": {"path": "test.py"}}]
                    )
                ]
            }
        }
        
        result_regular = workflow_instance._route_agent_to_tools(test_state_regular)
        
        if result_regular == "repository_explorer_tools":
            print("✅ Regular tool calls correctly route to tools execution")
        else:
            print(f"❌ Regular tool calls incorrectly route to: {result_regular}")
            return False
        
        print("✅ Routing logic correctly handles both HandoverTool and regular tools!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing routing logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_handover_tool_definition():
    """Test that HandoverTool is properly defined and accessible."""
    try:
        from duo_workflow_service.tools.handover import HandoverTool
        
        print("\n🛠️  Testing HandoverTool definition...")
        
        # Check that HandoverTool has the expected attributes
        if hasattr(HandoverTool, 'tool_title'):
            print(f"✅ HandoverTool.tool_title = '{HandoverTool.tool_title}'")
        else:
            print("❌ HandoverTool missing tool_title attribute")
            return False
        
        # Check that it's the expected value
        if HandoverTool.tool_title == "handover_tool":
            print("✅ HandoverTool.tool_title has correct value")
        else:
            print(f"❌ HandoverTool.tool_title has unexpected value: {HandoverTool.tool_title}")
            return False
        
        print("✅ HandoverTool is properly defined!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing HandoverTool definition: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests to verify the HandoverTool fix."""
    print("🧪 Testing Context 2.0 HandoverTool Fix")
    print("=" * 50)
    
    tests = [
        ("HandoverTool Definition", test_handover_tool_definition),
        ("HandoverTool in Toolsets", test_handover_tool_in_toolsets),
        ("Routing Logic", test_routing_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The HandoverTool fix should resolve the infinite loop issue.")
        print("\n💡 Key fixes applied:")
        print("   1. Added 'handover_tool' to all specialist agents' shared_tools")
        print("   2. Updated routing logic to check for HandoverTool calls")
        print("   3. Agents can now properly signal completion and avoid infinite loops")
        return True
    else:
        print("⚠️  Some tests failed. The fix may need additional work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
